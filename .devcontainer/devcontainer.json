{"name": "Emscripten", "build": {"dockerfile": "Dockerfile"}, "remoteEnv": {"VISUAL": "code -w"}, "mounts": [{"source": "${localWorkspaceFolder}/../neverball-packages", "target": "/workspaces/neverball-packages", "type": "bind"}], "customizations": {"vscode": {"extensions": ["ms-vscode.cpptools", "eamodio.gitlens", "ritwickdey.liveserver"], "settings": {"C_Cpp.default.compilerPath": "/emsdk/upstream/emscripten/emcc", "C_Cpp.default.cStandard": "c99", "C_Cpp.default.systemIncludePath": ["/emsdk/upstream/emscripten/cache/sysroot/include", "/emsdk/upstream/emscripten/cache/sysroot/include/SDL2", "/opt/gl4es/include"], "C_Cpp.default.defines": ["__EMSCRIPTEN__=1"], "files.trimTrailingWhitespace": true, "diffEditor.renderSideBySide": false, "files.associations": {"Makefile.emscripten": "makefile"}, "liveServer.settings.root": "/js", "remote.localPortHost": "allInterfaces"}}}}