<?xml version="1.0"?>
<!--
build commands
[RadiantPath]: path to Radiant .. C:\Program Files\Gtkradiant
[EnginePath]: path to the engine .. C:\quake3\ C:\Program Files\Quake III Arena\ /usr/local/games/quake3/
-->
<!-- grep for build_set_variable in Radiant source to see other vars -->
<project version="2.0">
<var name="mapc">"[EnginePath]mapc"</var>
<var name="neverball">"[EnginePath]neverball"</var>
<var name="neverputt">"[EnginePath]neverputt"</var>
<var name="data">"[EnginePath][GameName]"</var>
<var name="opts">--bcast</var>
<build name="Compile">
<command>[mapc] "[MapFile]" [data] [opts]</command>
</build>
<build name="Neverball: play">
<command>[neverball] "[MapFile]"</command>
</build>
<build name="Neverball: compile+play">
<command>[mapc] "[MapFile]" [data] [opts]</command>
<command>[neverball] "[MapFile]"</command>
</build>
<build name="Neverputt: play">
<command>[neverputt] "[MapFile]"</command>
</build>
<build name="Neverputt: compile+play">
<command>[mapc] "[MapFile]" [data] [opts]</command>
<command>[neverputt] "[MapFile]"</command>
</build>
</project>

