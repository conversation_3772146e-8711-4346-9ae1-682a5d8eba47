
                            * Neverball *


* REQUIREMENTS

    SDL 2.0           http://libsdl.org/download-2.0.php
    SDL2_ttf          http://libsdl.org/projects/SDL_ttf/
    libvorbis         http://xiph.org/vorbis/
    libpng            http://libpng.org/pub/png/libpng.html
    libjpeg           http://ijg.org/
    libcurl           optional, see below
    libintl           optional, see below


* COMPILATION

Under Unix and Linux, simply run

    make

Under Windows, install the MSYS2 environment and run

    make

Cross-compilation for Windows is also supported. On Fedora Linux, run

    make sols clean-src
    mingw32-make -o sols PLATFORM=mingw


* OPTIONAL FEATURES

Optional features  can be enabled  at compile  time by passing  one or
more additional  arguments to  make.  Most  of these  features require
additional libraries to be installed.

make ENABLE_NLS=0
    Native language support.  This is  enabled by default. Requires an
    additional library on non-GLIBC systems.

    libintl           http://www.gnu.org/software/gettext/

make ENABLE_FETCH=curl
    Use libcurl for package downloads. This is enabled by default. Set
    this to any other value (e.g., ENABLE_FETCH=0) to disable downloads.

    libcurl           https://curl.se/libcurl/

make ENABLE_TILT=wii
    Nintendo Wii Remote support on Linux.

    BlueZ             http://www.bluez.org/
    libwiimote        http://libwiimote.sourceforge.net/

make ENABLE_TILT=loop
    Hillcrest Labs Loop support.

    libusb-1.0        http://libusb.org/wiki/Libusb1.0
    libfreespace      http://libfreespace.hillcrestlabs.com/

make ENABLE_TILT=leapmotion
    Leap Motion support on Linux.

    V2 SDK            https://developer.leapmotion.com/

make ENABLE_HMD=openhmd
    Head-mounted display support (including the Oculus Rift).

    OpenHMD           http://openhmd.net/

make ENABLE_HMD=libovr
    Oculus Rift support.

    Oculus SDK        https://developer.oculusvr.com/

make ENABLE_RADIANT_CONSOLE=1
    Map compiler output to Radiant console.

    SDL2_net          http://www.libsdl.org/projects/SDL_net/


* INSTALLATION

By default,  an uninstalled build may  be executed in place.  A system
wide  installation   on  Linux  would   probably  copy  the   game  to
/opt/neverball.

To  be able  to use  the NetRadiant  level editor,  the game  data and
binaries must be  installed in the same  location.  Distributions that
wish to package Neverball, Neverputt  and their shared data separately
should take care to use symlinks and launcher scripts to support this.


* DISTRIBUTION

The dist subdirectory contains some miscellaneous files:

  * .desktop files
  * high resolution icons in PNG, SVG and ICO formats


Web: <http://neverball.org/>
