<?xml version="1.0"?>
<!-- Neverball entity definition file -->
<classes>
<group name="func_train" color="0 0.5 0.8">
Defines the geometry of a moving object.  Think of it as a container or a grouping mechanism for lumps, rather than an object in-and-of itself.

<target key="target" name="Path">Path that the train will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target2" name="Path (rotation)">An optional second path that controls the orientation of the train, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
</group>


<point name="info_camp" color="0 0.5 0" model="{{ state == 1 -> {'path': 'obj/editor/switch1.obj', 'scale': 1}, {'path': 'obj/editor/switch.obj', 'scale': 1} }}" box="-32 -32 0 32 32 128">
A switch.

<target key="target" name="Target">Refers to the path that the switch controls.</target>
<target key="target2" name="Path">Path that the switch will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target3" name="Path (rotation)">An optional second path that controls the orientation of the switch, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
<real key="radius" name="Radius" value="0.5">Switch radius in meters.  The default is "0.5".</real>
<boolean key="state" name="State" value="0">Initial state of the switch.  "0" is off, "1" is on.  The default is off.  This parallels the state attribute of the path_corner.  An info_camp entity should always have the same initial "state" value as the path_corner it targets.</boolean>
<real key="timer" name="Timer" value="0">A delay time.  The time begins when a switch is toggled to its non-initial state.  The switch toggles back to its initial state when the timer expires.  A timer value of zero indicates an untimed switch.  The default is zero.  This may be used to define a door that opens only for a moment before closing, or a func_train that moves along its path in discrete activated steps.</real>
<boolean key="invisible" name="Invisible" value="0">Determines whether the switch is invisible or not.  "0" is off, "1" is on.  The default is off.</boolean>
</point>


<point name="info_null" color="0 0.5 0" box="-8 -8 -8 8 8 8">
A billboard.

<target key="target" name="Path">Path that the billboard will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target2" name="Path (rotation)">An optional second path that controls the orientation of the billboard, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
</point>


<point name="info_player_deathmatch" color="0.75 0.75 0" model="{'path': 'obj/editor/goal.obj', 'scale': 1}" box="-48 -48 -24 48 48 168">
A goal.

<real key="radius" name="Radius" value="0.75">Goal radius in meters.  The default radius is "0.75".  The ball must fall entirely within this radius to trigger a goal.</real>
<target key="target" name="Path">Path that the goal will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target2" name="Path (rotation)">An optional second path that controls the orientation of the goal, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
</point>


<point name="info_player_intermission" color="1 0 1" box="-16 -16 -16 16 16 16">
Defines the camera position at the beginning of a level fly-in

<target key="target" name="Target">Refers to the target_position defining the view direction.</target>
</point>


<point name="info_player_start" color="1 1 1" model="{'path': 'obj/editor/ball.obj', 'scale': 1}" box="-16 -16 -24 16 16 8">
A ball.  While multiple balls may be defined, Neverball uses only the first of them.  Neverputt requires an info_player_start entity for each player slot plus one.

<real key="radius" name="Radius" value="0.25">Ball radius in meters.  The default radius is "0.25".  In Neverputt, this should be set to "0.0625".</real>
</point>


<point name="item_health_large" color="1 0 0" model="{'path': 'obj/editor/grow.obj', 'scale': 0.125}" box="-8 -8 -16 8 8 16">
A grow coin.

<target key="target" name="Path">Path that the item will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target2" name="Path (rotation)">An optional second path that controls the orientation of the item, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
</point>


<point name="item_health_small" color="0 1 0" model="{'path': 'obj/editor/shrink.obj', 'scale': 0.125}" box="-8 -8 -16 8 8 16">
A shrink coin.

<target key="target" name="Path">Path that the item will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target2" name="Path (rotation)">An optional second path that controls the orientation of the item, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
</point>


<list name="clock">
  <item name="None" value="0"/>
  <item name="Small" value="5"/>
  <item name="Medium" value="15"/>
  <item name="Large" value="30"/>
</list>

<point name="item_clock" color="0.5 0.25 1"  box="-8 -8 -16 8 8 16">
A clock. Adds time to a level.

<clock key="light" name="Preset bonus" value="0">Value of the clock.  Pink clock (small) = 5, turquoise clock (medium) = 15, orange clock (large) = 30.</clock>

<target key="target" name="Path">Path that the item will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target2" name="Path (rotation)">An optional second path that controls the orientation of the item, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
</point>


<list name="coin">
  <item name="Yellow" value="1"/>
  <item name="Red" value="5"/>
  <item name="Blue" value="10"/>
</list>

<point name="light" color="1.0 1.0 0" model="{{ light >= 10 -> {'path': 'obj/editor/coin10.obj', 'scale': 1}, light >= 5 -> {'path': 'obj/editor/coin5.obj', 'scale': 1}, {'path': 'obj/editor/coin.obj', 'scale': 1} }}" box="-8 -8 -16 8 8 16">
A coin.

<coin key="light" name="Value" value="1">Value of the coin.  Yellow coin = 1, red coin = 5, blue coin = 10.</coin>
<target key="target" name="Path">Path that the item will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target2" name="Path (rotation)">An optional second path that controls the orientation of the item, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
</point>


<point name="misc_model" color="1 0.5 0.25" model="model" box="-8 -8 -8 8 8 8">
Imports an arbitrary polygonal model into a level.  It may be used to increase the visual detail of a level without increasing the physical detail.  Arbitrary normal vectors are allowed, so the misc_model entity can be used to produce smoothly shaded curves.

misc_model entities define visible geometry, but not physical geometry.  So, if the ball is to bounce off of a misc_model entity, the entity should be placed within one or more invisible structural lumps.

<model key="model" name="Model">Path to the model file.  The model must be in OBJ format.  It must have triangular tessellation.  All vertices must have normals and texture coordinates.</model>
<target key="target" name="Path">Path that the model will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target2" name="Path (rotation)">An optional second path that controls the orientation of the model, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
</point>


<point name="path_corner" color="0.5 0.3 0" box="-8 -8 -8 8 8 8">
A path segment of a moving object.

<target key="target" name="Target">Refers to another path_corner defining the destination of the path.</target>
<targetname key="targetname" name="Target name">Name used by other path_corner entities to target this entity.</targetname>
<real key="speed" name="Travel time" value="1.0">Duration of the trip along this path segment, in seconds.  The default is "1.0".</real>
<boolean key="state" name="State" value="1">Determines whether the path is enabled.  An object will only move along an enabled path.  An object may be stopped and started by toggling the state of the path to which it is attached.  "0" means no, "1" means yes.  The default is 1.</boolean>
<boolean key="smooth" name="Smooth" value="1">Determines whether an object moves smoothly along this path segment or at a constant speed.  "0" means no, "1" means yes.  The default is 1.</boolean>
<angle key="angle" name="Orientation (yaw)" value="0">See "angles".  This attribute is supported only for Radiant compatibility.</angle>
<angles key="angles" name="Orientation (pitch, yaw, roll)" value="0 0 0">Defines the final orientation of the object at this path_corner.  The values are angles giving pitch (up/down), yaw (left/right), and roll.  (Use Radiant's rotation manipulator to set the general orientation, and then adjust the values manually.)  As the object travels between two path corners, its orientation is interpolated between the orientations of the corners.</angles>
</point>


<point name="target_position" color="0 0.5 0" box="-8 -8 -8 8 8 8">
Defines the initial view direction of a level fly-in, as well as the destination of a teleporter.

<targetname key="targetname" name="Target name">Name used by other entities to target this entity.</targetname>
</point>


<point name="target_teleporter" color="0 0.5 0" model="{'path': 'obj/editor/jump.obj', 'scale': 1}" box="-32 -32 0 32 32 128">
A teleporter.

<target key="target" name="Target">Refers to a target_position entity defining the destination of the teleporter.</target>
<target key="target2" name="Path">Path that the teleporter will travel along, starting from the linked path_corner. Both position and rotation are controlled by this path.</target>
<target key="target3" name="Path (rotation)">An optional second path that controls the orientation of the teleporter, but has no effect on position. This is useful when you want to control the position and orientation independently of each other.</target>
<real key="radius" name="Radius" value="0.5">Teleporter radius.  The default is "0.5".  The ball must fall entirely within this radius to trigger the teleport.</real>
</point>

<list name="background">
  <item name="Alien" value="map-back/alien.sol"/>
  <item name="City" value="map-back/city.sol"/>
  <item name="Clouds" value="map-back/clouds.sol"/>
  <item name="Jupiter" value="map-back/jupiter.sol"/>
  <item name="Ocean" value="map-back/ocean.sol"/>
  <item name="Volcano" value="map-back/volcano.sol"/>
</list>

<list name="gradient">
  <item name="Alien" value="back/alien.png"/>
  <item name="City" value="back/city.png"/>
  <item name="Land" value="back/land.png"/>
  <item name="Ocean" value="back/ocean.png"/>
  <item name="Pastel" value="back/pastel.png"/>
  <item name="Space" value="back/space.png"/>
  <item name="Volcano" value="back/volcano.png"/>
</list>

<list name="song">
  <item name="Title" value="bgm/title.ogg"/>
  <item name="Track1" value="bgm/track1.ogg"/>
  <item name="Track2" value="bgm/track2.ogg"/>
  <item name="Track3" value="bgm/track3.ogg"/>
  <item name="Track4" value="bgm/track4.ogg"/>
  <item name="Track5" value="bgm/track5.ogg"/>
  <item name="Track6" value="bgm/track6.ogg"/>
</list>

<group name="worldspawn" color="0 0 0">
Used for most static level geometry.

<string key="message" name="Introduction">Text that appears as a level begins.  A "\" (backslash) starts a new line.</string>
<background key="back" name="Background environment">Path to the background file.</background>
<gradient key="grad" name="Background gradient">Path to the background gradient image file.</gradient>
<song key="song" name="Background music">Path to the background music file.</song>
<string key="shot" name="Level shot">Path to the level shot file.</string>
<integer key="goal" name="Required coins">Number of coins needed to unlock the goal.</integer>
<integer key="time" name="Time limit">Level time limit in hundredths of a second.</integer>
<string key="time_hs" name="Best Times">Default values for Best Times highscore, in order: Hard, Medium, and Easy (optional, defaults to time limit).</string>
<string key="goal_hs" name="Fast Unlock">Default values for Fast Unlock highscore, in order: Hard, Medium, and Easy (optional, defaults to time limit).</string>
<string key="coin_hs" name="Most Coins">Default values for Most Coins highscore, in order: Hard, Medium, and Easy (optional, defaults to required coins).</string>
<string key="version" name="Version">Version of the level.  The format should be "X.Y", where X is incremented every time the level is changed in a way that breaks existing replays, and Y is incremented for all other changes.</string>
<string key="author" name="Author">Name of the author.</string>
<boolean key="bonus" name="Bonus level">Marks this level as a bonus level.</boolean>
<real key="idle" name="Idling time-out (Neverputt)">Neverputt: time to wait after the ball has stopped before starting the next shot.  Useful to ensure that moving objects won't hit the ball while the player is making the shot.</real>
<integer key="par" name="Par (Neverputt)">Neverputt: the number of strokes required to complete the hole.</integer>
</group>
</classes>
