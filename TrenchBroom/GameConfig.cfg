{
    "version": 9,
    "name": "Neverball",
    "icon": "neverball.png",
    "fileformats": [
        { "format": "Quake3" }
    ],
    "filesystem": {
        "searchpath": "data",
        "packageformat": { "extensions": [".zip", ".pk3"], "format": "zip" }
    },
    "materials": {
        "root": "textures",
        "extensions": ["png", "jpg"],
        "shaderSearchPath": "textures"
    },
    "entities": {
        "definitions": [ "entities.ent" ],
        "defaultcolor": "0.6 0.6 0.6 1.0",
        "scale": 64
    },
    "tags": {
        "brush": [
        ],
        "brushface": [
            {
                "name": "Invisible",
                "attribs": ["transparent"],
                "match": "material",
                "pattern": "invisible"
            },
            {
                "name": "Glass",
                "attribs": ["transparent"],
                "match": "material",
                "pattern": "glass*"
            },
            {
                "name": "Trim",
                "attribs": ["transparent"],
                "match": "material",
                "pattern": "edge"
            },
            {
                "name": "Detail",
                "match": "contentflag",
                "flags": [ "detail" ]
            }
        ]
    },
    "compilationTools": [
        { "name": "mapc", "description": "Path to mapc, the Neverball map compiler" }
    ],
    "faceattribs": {
        "defaults": {
                "scale": [0.5, 0.5]
        },
        "surfaceflags": [
        ],
        "contentflags": [
            // Setting ANY flags here will cause the brush to turn into a detail brush.
            // However, it's not wise to rely on this.
            { "unused": true }, // 1
            { "unused": true }, // 2
            { "unused": true }, // 4
            { "unused": true }, // 8
            { "unused": true }, // 16
            { "unused": true }, // 32
            { "unused": true }, // 64
            { "unused": true }, // 128
            { "unused": true }, // 256
            { "unused": true }, // 512
            { "unused": true }, // 1024
            { "unused": true }, // 2048
            { "unused": true }, // 4096
            { "unused": true }, // 8192
            { "unused": true }, // 16384
            { "unused": true }, // 32768
            { "unused": true }, // 65536
            { "unused": true }, // 131072
            { "unused": true }, // 262144 c0
            { "unused": true }, // 524288 c9
            { "unused": true }, // 1048576 c1
            { "unused": true }, // 2097152 c2
            { "unused": true }, // 4194304 cu
            { "unused": true }, // 8388608 cd
            { "unused": true }, // 16777216 o
            { "unused": true }, // 33554432 m
            { "unused": true }, // 67108864 c
            {
                "name": "detail",
                "description": "The brush cannot be collided with."
            }
        ]
    }
}
