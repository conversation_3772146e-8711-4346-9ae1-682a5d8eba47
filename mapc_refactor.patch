--- share/mapc.c.backup
+++ share/mapc.c
@@ -2097,7 +2097,8 @@
  * verts to  have a counter-clockwise winding about  the plane normal.
  * Create geoms to tessellate the resulting convex polygon.
  */
-static void clip_geom(struct s_base *fp,
+static void clip_geom(struct mapc_context *ctx, struct s_base *fp,
                       struct b_lump *lp, int si)
 {
     int   m[256], t[256], d, i, j, n = 0;
@@ -2118,9 +2119,9 @@
             m[n] = vi;
             t[n] = inct(fp);
 
-            v_add(v, fp->vv[vi].p, plane_p[si]);
+            v_add(v, fp->vv[vi].p, ctx->plane_p[si]);
 
-            fp->tv[t[n]].u[0] = v_dot(v, plane_u[si]);
-            fp->tv[t[n]].u[1] = v_dot(v, plane_v[si]);
+            fp->tv[t[n]].u[0] = v_dot(v, ctx->plane_u[si]);
+            fp->tv[t[n]].u[1] = v_dot(v, ctx->plane_v[si]);
 
             if (++n >= ARRAYSIZE(m))
             {
@@ -2164,7 +2165,7 @@
         struct b_offs *oq = fp->ov + (gp->oj = inco(fp));
         struct b_offs *or = fp->ov + (gp->ok = inco(fp));
 
-        gp->mi = plane_m[si];
+        gp->mi = ctx->plane_m[si];
 
         op->ti = t[0];
         oq->ti = t[i + 1];
@@ -2189,7 +2190,7 @@
  * each trio of planes, a new edge  for each pair of planes, and a new
  * set of geom for each visible plane.
  */
-static void clip_lump(struct s_base *fp, struct b_lump *lp)
+static void clip_lump(struct mapc_context *ctx, struct s_base *fp, struct b_lump *lp)
 {
     int i, j, k;
 
@@ -2217,15 +2218,15 @@
     lp->gc = 0;
 
     for (i = 0; i < lp->sc; i++)
-        if (fp->mv[plane_m[fp->iv[lp->s0 + i]]].d[3] > 0.0f)
-            clip_geom(fp, lp,
+        if (fp->mv[ctx->plane_m[fp->iv[lp->s0 + i]]].d[3] > 0.0f)
+            clip_geom(ctx, fp, lp,
                       fp->iv[lp->s0 + i]);
 
     for (i = 0; i < lp->sc; i++)
-        if (plane_f[fp->iv[lp->s0 + i]])
+        if (ctx->plane_f[fp->iv[lp->s0 + i]])
             lp->fl |= L_DETAIL;
 }
 
-static void clip_file(struct s_base *fp)
+static void clip_file(struct mapc_context *ctx, struct s_base *fp)
 {
     int i;
 
     for (i = 0; i < fp->lc; i++)
-        clip_lump(fp, fp->lv + i);
+        clip_lump(ctx, fp, fp->lv + i);
 }
